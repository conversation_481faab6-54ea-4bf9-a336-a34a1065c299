import { type Auth0ContextInterface, User } from "@auth0/auth0-react"
import { Outlet, createRootRouteWithContext } from "@tanstack/react-router"

import { UseGameStoreProps } from "../store/useGamesStore"
import { UseUserStoreProps } from "../store/useUserStore"
import { trpc } from "../trpc/trpc"

interface MyRouterContext {
  trpc: typeof trpc
  auth0: Auth0ContextInterface<User>
  userData: null | UseUserStoreProps
  gameStore: null | UseGameStoreProps
}

export const rootRoute = createRootRouteWithContext<MyRouterContext>()({
  component: Outlet,
})
