import { Auth0Provider } from "@auth0/auth0-react"
import { StyledEngineProvider } from "@mui/material"
import isMobile from "is-mobile"
import { useEffect } from "react"

import { UserDataLoader } from "./components/UserDataLoader/UserDataLoader"
import { RouterProviderWithContext, router } from "./routes/router"
import { useIsMobileStore } from "./store/useIsMobileStore"
import { useUserStore } from "./store/useUserStore"

export const App = () => {
  const { userLoaded } = useUserStore()
  const { setIsMobile, setCurrentWidth } = useIsMobileStore()

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(isMobile())
      setCurrentWidth(window.innerWidth)
    }
    handleResize()
    window.addEventListener("resize", handleResize)

    return () => {
      window.removeEventListener("resize", handleResize)
    }
  }, [setIsMobile])

  return (
    <Auth0Provider
      domain={ENV_AUTH0_DOMAIN}
      clientId={ENV_AUTH0_CLIENT_ID}
      authorizationParams={{
        redirect_uri: ENV_AUTH0_REDIRECT_URI,
        audience: ENV_AUTH0_AUDIENCE,
      }}
      onRedirectCallback={(appState) => {
        router.navigate(appState?.navigateTo ?? { to: "/" })
      }}
    >
      <StyledEngineProvider injectFirst>
        <UserDataLoader />
        {userLoaded && <RouterProviderWithContext />}
      </StyledEngineProvider>
    </Auth0Provider>
  )
}
