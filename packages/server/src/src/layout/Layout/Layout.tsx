import { Box } from "@mui/material"

import { Breadcrumbs } from "../../components/Breadcurmbs/Breadcrumbs"
import { FillData } from "../../components/FillData/FillData"
import { useIsMobileStore } from "../../store/useIsMobileStore"

import * as styles from "./layout.css"

export const Layout = ({ children }: { children: React.ReactNode }) => {
  const isMobile = useIsMobileStore((state) => state.isMobile)

  return (
    <Box className={styles.container}>
      <FillData />
      {!isMobile && <Breadcrumbs />}
      <div className={styles.layout}>{children}</div>
    </Box>
  )
}
