import { useAuth0 } from "@auth0/auth0-react"
import { Button } from "@mui/material"

import { useUserStore } from "../../store/useUserStore"

export const LoginButton = () => {
  const isLoggedIn = useUserStore((state) => state.isLoggedIn)
  const { loginWithRedirect, isLoading, logout } = useAuth0()

  if (isLoading) {
    return null
  }
  if (isLoggedIn) {
    return (
      <Button variant="outlined" color="inherit" onClick={() => logout()}>
        Logout
      </Button>
    )
  }

  return (
    <Button
      variant="outlined"
      color="inherit"
      onClick={() => loginWithRedirect()}
    >
      Sign on/Log In
    </Button>
  )
}
