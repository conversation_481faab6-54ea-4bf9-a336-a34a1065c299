import { Breadcrumbs as MUIBreadcrumbs, Typography } from "@mui/material"

import { useBreadcrumbsStore } from "../../store/useBreadcrumbsStore"
import { PartyLink } from "../elements/link/PartyLink/PartyLink"

import * as styles from "./breadcrumbs.css"

export const Breadcrumbs = () => {
  const breadcrumbs = useBreadcrumbsStore((state) => state.breadcrumbs)

  if (breadcrumbs === null) return null

  return (
    <MUIBreadcrumbs maxItems={6} aria-label="breadcrumb">
      {breadcrumbs.map((link) => {
        return link.current ? (
          <Typography
            variant="caption"
            key={link.to}
            className={styles.current}
          >
            {link.name}
          </Typography>
        ) : (
          <PartyLink key={link.to} to={link.to} params={link.params}>
            {link.name}
          </PartyLink>
        )
      })}
    </MUIBreadcrumbs>
  )
}
