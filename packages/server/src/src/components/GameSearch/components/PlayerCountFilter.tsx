import {
  Box,
  Button,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  type SelectChangeEvent,
  Typography,
} from "@mui/material"
import React from "react"

import * as styles from "./playerCountFilter.css"

interface PlayerCountFilterProps {
  minPlayers?: number
  maxPlayers?: number
  onChangeMin: (min: number) => void
  onChangeMax: (max: number) => void
  onSetPlayerLevel: (value: number) => void
  playerLevel?: number
}

const playerLevelCycle = ["Exact", "Best", "Recommend", "Box"]

export const PlayerCountFilter = ({
  minPlayers,
  maxPlayers,
  onChangeMin,
  onChangeMax,
  playerLevel,
  onSetPlayerLevel,
}: PlayerCountFilterProps) => {
  const currentCountLevel = playerLevel ?? 0

  const newCountLevel = currentCountLevel < 3 ? currentCountLevel + 1 : 0
  const handleChangeMin = (event: SelectChangeEvent<string>) =>
    onChangeMin(parseInt(event.target.value))

  const handleChangeMax = (event: SelectChangeEvent<string>) =>
    onChangeMax(parseInt(event.target.value))

  return (
    <Box className={styles.container}>
      <FormControl fullWidth>
        <InputLabel id="min-by-label">
          {currentCountLevel > 0 ? "Min" : "Pick"}
        </InputLabel>
        <Select
          labelId="min-by-label"
          id="min-by-select"
          value={String(minPlayers ?? -1)}
          label={currentCountLevel > 0 ? "Min" : "Pick"}
          onChange={handleChangeMin}
        >
          <MenuItem value="-1">-</MenuItem>
          <MenuItem value="1">1</MenuItem>
          <MenuItem value="2">2</MenuItem>
          <MenuItem value="3">3</MenuItem>
          <MenuItem value="4">4</MenuItem>
          <MenuItem value="5">5</MenuItem>
          <MenuItem value="6">6</MenuItem>
          <MenuItem value="7">7</MenuItem>
          <MenuItem value="8">8</MenuItem>
          <MenuItem value="10">10</MenuItem>
          <MenuItem value="15">15</MenuItem>
        </Select>
      </FormControl>
      {currentCountLevel > 0 && (
        <FormControl fullWidth>
          <InputLabel id="max-by-label">Max</InputLabel>
          <Select
            labelId="max-by-label"
            id="max-by-select"
            value={String(maxPlayers ?? -1)}
            label="Max"
            onChange={handleChangeMax}
          >
            <MenuItem value="-1">-</MenuItem>
            <MenuItem value="1">1</MenuItem>
            <MenuItem value="2">2</MenuItem>
            <MenuItem value="3">3</MenuItem>
            <MenuItem value="4">4</MenuItem>
            <MenuItem value="5">5</MenuItem>
            <MenuItem value="6">6</MenuItem>
            <MenuItem value="7">7</MenuItem>
            <MenuItem value="8">8</MenuItem>
            <MenuItem value="10">10</MenuItem>
            <MenuItem value="15">15</MenuItem>
          </Select>
        </FormControl>
      )}
      <Box className={styles.buttonContainer}>
        <Typography variant="caption" className={styles.label}>
          Player count
        </Typography>
        <Button
          variant="outlined"
          className={styles.button}
          size="small"
          onClick={() => onSetPlayerLevel(newCountLevel)}
        >
          {playerLevelCycle[currentCountLevel]}
        </Button>
      </Box>
    </Box>
  )
}
