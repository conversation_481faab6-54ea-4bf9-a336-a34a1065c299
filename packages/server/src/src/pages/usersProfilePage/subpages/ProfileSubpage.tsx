import { useAuth0 } from "@auth0/auth0-react"
import { zodResolver } from "@hookform/resolvers/zod"
import { LoadingButton } from "@mui/lab"
import {
  Alert,
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  Grid2,
  Typography,
} from "@mui/material"
import TextField from "@mui/material/TextField"
import { useRouter } from "@tanstack/react-router"
import React, { ChangeEvent, useState } from "react"
import {
  Controller,
  FormProvider,
  type SubmitHandler,
  useForm,
} from "react-hook-form"
import { z } from "zod"

import {
  LoaderDialog,
  LoaderDialogState,
} from "../../../components/LoaderDialog/LoaderDialog"
import { UserAvatar } from "../../../components/UserAvatar/UserAvatar"
import { FormInput } from "../../../components/elements/HookElements/FormInput"
import { FormSwitch } from "../../../components/elements/HookElements/FormSwitch"
import { usersProfileRoute } from "../../../routes/usersProfile.route"
import { UploadImageInput, uploadImageInput } from "../../../schemas"
import { trpc } from "../../../trpc/trpc"

const schema = z.object({
  name: z.string().min(3),
  bggUsername: z.union([z.literal(""), z.string().min(4).max(20).optional()]),
  email: z.union([z.literal(""), z.string().email()]),
  color: z.string().regex(/^[0-9a-f]{6}$/i),
  active: z.boolean(),
})

type ProfileFormInputs = z.infer<typeof schema>

export const ProfileSubpage = () => {
  const user = usersProfileRoute.useLoaderData()
  const router = useRouter()
  const { isAuthenticated, getAccessTokenSilently } = useAuth0()
  const [imageChanged, setImageChanged] = useState(0)
  const [mustWaitWarning, setMustWaitWarning] = useState(false)
  const [savingImage, setSavingImage] = useState<LoaderDialogState>(null)
  const [savingData, setSavingData] = useState<LoaderDialogState>(null)

  if (!user) {
    return null
  }

  const { profile } = user

  const methods = useForm<ProfileFormInputs>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: profile.name,
      bggUsername: profile.bggUsername ?? "",
      email: profile.email ?? "",
      color: profile.color ?? "",
      active: profile.active ?? false,
    },
  })

  const { handleSubmit: handleSubmitUpload, control: controlUpload } =
    useForm<UploadImageInput>({
      resolver: zodResolver(uploadImageInput),
      defaultValues: {
        image: "",
      },
    })

  const onSubmit: SubmitHandler<ProfileFormInputs> = (data) => {
    setSavingData("loading")

    trpc.userProfileUpdate
      .mutate(data)
      .then(() => {
        setSavingData(null)
        if (data.bggUsername !== profile.bggUsername) {
          setMustWaitWarning(true)
        }
      })
      .catch(() => {
        setSavingData("failed")
      })
  }

  const onSubmitUpload: SubmitHandler<UploadImageInput> = async (data) => {
    // do the upload
    const formData = new FormData()
    formData.append("image", data.image)
    setSavingImage("loading")
    if (isAuthenticated) {
      fetch(`${ENV_API_URL}/changeProfile`, {
        method: "POST",
        body: formData,
        headers: {
          "Access-Control-Allow-Origin": "*",
          Authorization: `Bearer ${await getAccessTokenSilently()}`,
        },
      })
        .then((response) => response.text())
        .then((text) => {
          if (text === "OK") {
            setImageChanged(imageChanged + 1)
          }
          setSavingImage(null)
          router.invalidate()
        })
        .catch((error) => {
          setSavingImage("failed")
        })
        .finally(() => {})
    }
    setSavingImage("failed")
  }

  return (
    <Box padding={4}>
      <FormControl>
        <form onSubmit={handleSubmitUpload(onSubmitUpload)}>
          <Grid2 container columns={1} spacing={2}>
            <Grid2 size={1}>
              <Box>
                <UserAvatar user={profile} size="large" />
              </Box>
            </Grid2>
            <Grid2 size={1}>
              <Controller
                name="image"
                control={controlUpload}
                render={({ field: { onChange }, fieldState: { error } }) => (
                  <FormControl error={!!error}>
                    <TextField
                      helperText={error ? error.message : null}
                      type="file"
                      error={!!error}
                      onChange={(event: ChangeEvent<HTMLInputElement>) => {
                        onChange(event.target.files)
                      }}
                      name="image"
                      fullWidth
                    />
                  </FormControl>
                )}
              />
            </Grid2>
            <Grid2 size={1}>
              <LoadingButton
                variant="contained"
                color="primary"
                type="submit"
                loading={savingImage === "loading"}
              >
                Change
              </LoadingButton>
            </Grid2>
          </Grid2>
        </form>
        <FormProvider {...methods}>
          <form onSubmit={methods.handleSubmit(onSubmit)}>
            <Grid2 container columns={1} spacing={2} marginTop={2}>
              <Grid2 size={1}>
                <FormInput
                  label="Name"
                  name="name"
                  required={true}
                  placeholder="John"
                />
              </Grid2>
              <Grid2 size={1}>
                <FormInput
                  label="BGG Username"
                  name="bggUsername"
                  placeholder="jons_username"
                  helper="Your BGG username. Fill if You want Your collection to be added here. Synchronization will take up to 2 hours."
                />
              </Grid2>
              <Grid2 size={1}>
                <FormInput
                  label="Email"
                  name="email"
                  placeholder="<EMAIL>"
                />
              </Grid2>
              <Grid2 size={1}>
                <FormInput label="Color" name="color" />
              </Grid2>
              <Grid2 size={1}>
                <FormSwitch
                  name={"active"}
                  label="Active"
                  helper="Deactivate my profile for all communities"
                />
              </Grid2>
              <Grid2 size={1}>
                <LoadingButton
                  variant="contained"
                  color="primary"
                  type="submit"
                  loading={savingData === "loading"}
                >
                  Save
                </LoadingButton>
              </Grid2>
            </Grid2>
          </form>
        </FormProvider>
      </FormControl>
      <Box gap={2} marginTop={2}>
        <Typography>Informative items</Typography>
        <Grid2 container columns={2} maxWidth={200}>
          <Grid2 size={1}>
            <Typography variant="body1" fontWeight={500}>
              Game count:
            </Typography>
          </Grid2>
          <Grid2 size={1}>
            <Typography>{profile.gameCount}</Typography>
          </Grid2>
        </Grid2>
      </Box>
      <LoaderDialog
        state={savingImage}
        title="Changing Your Image"
        onClose={() => {
          setSavingImage(null)
        }}
      />
      <LoaderDialog
        state={savingData}
        title="Updating Your Info"
        onClose={() => {
          setSavingData(null)
        }}
      />
      <Dialog open={mustWaitWarning}>
        <DialogTitle>Game information</DialogTitle>
        <DialogContent>
          <Alert severity="warning">
            Your BGG information will take some time to be updated - it can take
            up to 2 hours.
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button variant="outlined" onClick={() => setMustWaitWarning(false)}>
            Ok, i got it!
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}
