import { Box } from "@mui/material"
import { useNavigate, useParentMatches } from "@tanstack/react-router"
import { useCallback, useEffect, useMemo } from "react"

import {
  GameSearch,
  SearchParams,
} from "../../components/GameSearch/GameSearch"
import { PartyLink } from "../../components/elements/link/PartyLink/PartyLink"
import { GamesList } from "../../components/gamesList/GamesList"
import { TitleRow } from "../../components/titleRow/TitleRow"
import { createRoutes } from "../../routes/breadcrumbs"
import { gamesRoute } from "../../routes/games.route"
import {
  COMMUNITIES_ROOT_ROUTE,
  COMMUNITY_ROUTE,
  COMMUNITY_USER_ROUTE,
  GAMES_ROUTE,
  GAME_ROUTE,
} from "../../routes/paths"
import { useBreadcrumbsStore } from "../../store/useBreadcrumbsStore"
import { isOrderBy, useGameStore } from "../../store/useGamesStore"
import { useIsMobileStore } from "../../store/useIsMobileStore"
import { applyFilters } from "../../utils/filter"

import * as styles from "./gamesPage.css"

export const GamesPage = () => {
  const base = useParentMatches().find(
    (match) => match.fullPath === COMMUNITIES_ROOT_ROUTE,
  )?.loaderData

  const sizeThresholdList = useIsMobileStore((state) => state.sizeThresholdList)

  const search = gamesRoute.useSearch()
  const linkParams = gamesRoute.useParams()

  const { getGames, setMeta } = useGameStore()

  const games = getGames(parseInt(linkParams.communityId))

  const navigate = useNavigate({ from: GAMES_ROUTE })
  const setBreadcrumbs = useBreadcrumbsStore((state) => state.setBreadcrumbs)

  useEffect(() => {
    createRoutes(
      setBreadcrumbs,
      "gamesRoute",
      {
        indexRoute: {},
        communitiesRoute: {},
        communityRoute: { communityId: linkParams.communityId },
        gamesRoute: {},
      },
      {
        $community: base?.name ?? "",
      },
    )
  }, [linkParams.communityId, base?.name])

  useEffect(() => {
    const order = search.order === "desc" ? "desc" : "asc"
    const orderBy =
      search.orderBy && isOrderBy(search.orderBy) ? search.orderBy : "title"
    setMeta({
      search: search.search ?? "",
      order,
      minPlayers: search.minPlayers,
      maxPlayers: search.maxPlayers,
      playerLevel: search.playerLevel ?? 1,
      orderBy,
      communityId: parseInt(linkParams.communityId),
    })
  }, [search, linkParams.communityId])

  const onClick = useCallback(
    (id?: number) =>
      navigate({
        to: COMMUNITY_USER_ROUTE,
        params: {
          userId: String(id),
        },
      }),
    [navigate],
  )

  if (!games) {
    return null
  }

  const useGameList = useMemo(() => {
    return applyFilters(
      games.games,
      games.meta,
      games.populatedTags,
      games.populatedUsers,
    )
  }, [games])

  const onNavigateGame = useCallback(
    (search: SearchParams) => {
      navigate({
        to: GAMES_ROUTE,
        search,
        params: linkParams,
      })
    },
    [navigate, linkParams],
  )

  const onChange = useCallback(
    (page: number) => {
      navigate({
        search: {
          page,
          minPlayers: games.meta.minPlayers,
          maxPlayers: games.meta.maxPlayers,
          playerLevel: games.meta.playerLevel,
          search: games.meta.search ?? "",
          order: games.meta.order,
          orderBy: games.meta.orderBy,
        },
      })

      window.scrollTo({ top: 0 })
    },
    [games.meta, navigate],
  )

  return (
    <>
      <Box className={styles.header}>
        <Box className={styles.searchBar}>
          <GameSearch
            onNavigate={onNavigateGame}
            search={search}
            tags={games.tags}
            tagCategories={games.tagCategories}
          />
        </Box>
        <TitleRow title="Games" className={styles.titleRow}>
          <PartyLink
            variant="outlined"
            to={COMMUNITY_ROUTE}
            params={{ communityId: String(base?.id ?? 0) }}
          >
            {sizeThresholdList.tablet ? "Back" : base?.name}
          </PartyLink>
        </TitleRow>
      </Box>
      <GamesList
        navigation={{
          to: GAME_ROUTE,
          params: {
            communityId: String(linkParams.communityId),
          },
        }}
        games={useGameList}
        onUser={onClick}
        onPageChange={onChange}
        page={search.page ?? 1}
      />
    </>
  )
}
