import { useNavigate } from "@tanstack/react-router"
import React, { useEffect } from "react"

import { CommunityConfiguration } from "../../components/CommunityConfiguration/CommunityConfiguration"
import { CommunityUploadImage } from "../../components/CommunityUploadImage/CommunityUploadImage"
import { UserDataLoader } from "../../components/UserDataLoader/UserDataLoader"
import { hasPermission } from "../../permissions"
import { COMMUNITY_ROUTE } from "../../routes/paths"
import { useUserStore } from "../../store/useUserStore"

export const CreateCommunityPage = () => {
  const [created, setCreated] = React.useState<null | number>(null)
  const [uploaded, setUploaded] = React.useState(false)
  const user = useUserStore((state) => state.userData)
  const navigate = useNavigate()
  if (
    !hasPermission(user, "global", "createCommunity", {
      createdCommunities: user.countCommunities,
    })
  ) {
    return null
  }

  useEffect(() => {
    if (created !== null && uploaded) {
      navigate({
        to: COMMUNITY_ROUTE,
        params: {
          communityId: String(created),
        },
      })
    }
  }, [created, uploaded])

  return (
    <>
      {!created && <CommunityConfiguration onSuccess={setCreated} />}
      {created && !uploaded && (
        <CommunityUploadImage
          onSuccess={() => setUploaded(true)}
          id={created}
        />
      )}
      {created && uploaded && <UserDataLoader />}
    </>
  )
}
