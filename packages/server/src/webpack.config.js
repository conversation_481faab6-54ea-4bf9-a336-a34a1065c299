const path = require("path")

const Autoprefixer = require("autoprefixer")
const dotenv = require("dotenv")
const HtmlWebpackPlugin = require("html-webpack-plugin")
const MiniCssExtractPlugin = require("mini-css-extract-plugin")
const PostcssModulesValues = require("postcss-modules-values")
const webpack = require("webpack")

const dev = process.env.NODE_ENV === "dev"

if (!dev) {
  dotenv.config({ path: "./.env" })
}

const plugins = [
  PostcssModulesValues,
  new webpack.DefinePlugin({
    ENV_API_URL: JSON.stringify(process.env.API_SERVER_URL),
    ENV_IMAGE_CDN: JSON.stringify(process.env.CDN_SERVER_URL),
    ENV_MODE: JSON.stringify(dev ? "dev" : "prod"),
    ENV_COMMUNITY_DEFAULT_LOGO: JSON.stringify(
      process.env.COMMUNITY_DEFAULT_LOGO,
    ),
    ENV_AUTH0_DOMAIN: JSON.stringify(process.env.AUTH0_DOMAIN),
    ENV_AUTH0_CLIENT_ID: JSON.stringify(process.env.AUTH0_CLIENT_ID),
    ENV_AUTH0_AUDIENCE: JSON.stringify(process.env.AUTH0_AUDIENCE),
    ENV_AUTH0_REDIRECT_URI: JSON.stringify(process.env.AUTH0_REDIRECT_URI),
  }),
  new MiniCssExtractPlugin({
    filename: "[name].[contenthash].css",
    chunkFilename: "[id].[contenthash].css",
  }),
  Autoprefixer,
  new HtmlWebpackPlugin({
    template: "src/index.ejs",
    title: "Party explain.games",
  }),
]

module.exports = {
  entry: "./src/index.tsx",
  mode: dev ? "development" : "production",
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        use: "ts-loader",
        exclude: /node_modules/,
      },
      {
        test: /\.svg$/,
        use: ["@svgr/webpack"],
      },
      {
        test: /\.css?$/i,
        use: [
          dev ? "style-loader" : MiniCssExtractPlugin.loader,
          {
            loader: "css-loader",
            options: {
              modules: {
                localIdentName: dev
                  ? "[name]__[local]___[hash:base64:5]"
                  : "[hash:base64]",
              },
              importLoaders: 1,
              sourceMap: dev,
            },
          },
          "postcss-loader",
        ],
      },
    ],
  },
  resolve: {
    extensions: [".tsx", ".ts", ".js", ".mjs", ".css", ".svg"],
  },
  optimization: dev
    ? undefined
    : {
        splitChunks: {
          // include all types of chunks
          chunks: "all",
          minSize: 1000,
          cacheGroups: {
            commons: {
              name: "commons",
              chunks: "initial",
              minChunks: 2,
            },
            vendors: {
              test: /[\\/]node_modules[\\/]/,
              name: "vendors",
              chunks: "all",
            },
          },
        },
      },
  output: {
    filename: "[name].[contenthash].js",
    publicPath: "/",
    path: dev
      ? path.resolve(__dirname, "build")
      : path.resolve(path.join(__dirname, "../../express/src/src/static")),
    clean: true,
  },
  devServer: {
    static: {
      directory: path.resolve(__dirname, "./build"),
      publicPath: "/assets",
    },
    client: {
      overlay: {
        errors: true,
        warnings: false,
        runtimeErrors: true,
      },
    },
    watchFiles: ["build/**/*"],
    compress: true,
    allowedHosts: "all",
    port: 443,
    liveReload: true,
    historyApiFallback: true,
  },
  watch: true,
  plugins,
}
