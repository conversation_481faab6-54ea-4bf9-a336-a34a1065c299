import { eq } from "drizzle-orm"
import { Request, Response } from "express"

import { db } from "../db"
import { usersSchema } from "../db/schema/users.schema"

import { uploadProfilePicture } from "./profilePicture"

const devUserAuthId = "google-oauth2|110962012279882593616"
export const uploadHandler = async (req: Request, res: Response) => {
  const authId =
    process.env.NODE_ENV === "development"
      ? devUserAuthId
      : req.auth?.payload?.sub

  if (!authId) {
    res.status(401).send("Unauthorized")
    return
  }

  const loginData = authId
    ? await db
        .select({ id: usersSchema.id })
        .from(usersSchema)
        .where(eq(usersSchema.authId, authId))
        .then((users) => {
          return users[0]
        })
    : undefined

  if (!loginData) {
    res.status(401).send("No User")
    return
  }

  if (!req.file) {
    res.status(400).send("No file")
    return
  }

  try {
    const fileExtension = req.file.originalname.split(".").pop()
    await uploadProfilePicture(
      req.file.buffer,
      loginData.id,
      fileExtension ?? "jpg",
    )

    await db
      .update(usersSchema)
      .set({
        avatar: `${loginData.id}.${fileExtension}?date=${Date.now()}`,
      })
      .where(eq(usersSchema.id, loginData.id))
  } catch (error: unknown) {
    res.status(500).send(`Error: ${error}`)
  }

  res.status(200).send("OK")
}
