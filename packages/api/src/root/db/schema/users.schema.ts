import {
  boolean,
  int,
  mysqlTable,
  timestamp,
  varchar,
} from "drizzle-orm/mysql-core"

import { commonCreated, commonId } from "./common"

export const usersSchema = mysqlTable("users", {
  name: varchar({ length: 255 }).notNull(),
  email: varchar({ length: 255 }).unique(),
  trust: int().notNull(),
  bggUsername: varchar("bgg_username", { length: 255 }),
  lastUpdated: timestamp("last_updated"),
  authId: varchar("auth_id", { length: 40 }).notNull(),
  avatar: varchar({ length: 255 }),
  color: varchar({ length: 6 }),
  active: boolean("active"),
  gameCount: int("total_games"),
  ...commonId,
  ...commonCreated,
})
