import { and, count, eq } from "drizzle-orm"
import * as uniqolor from "uniqolor"

import { db } from "../db"
import { permissionUserToRoleSchema } from "../db/schema/permissionUserToRole.schema"
import { roleSettingsSchema } from "../db/schema/roleSettings.schema"
import { usersSchema } from "../db/schema/users.schema"

import { loginProcedure } from "./trpc"

type NewUser = typeof usersSchema.$inferInsert

export const loggedInUser = loginProcedure.query(async ({ ctx: { auth } }) => {
  const currentUser = await db
    .select({
      id: usersSchema.id,
      name: usersSchema.name,
      email: usersSchema.email,
      bggUsername: usersSchema.bggUsername,
      avatar: usersSchema.avatar,
      color: usersSchema.color,
      trust: usersSchema.trust,
    })
    .from(usersSchema)
    .where(eq(usersSchema.authId, auth.payload.sub ?? ""))
    .then(async (user) => {
      let userData = user[0]

      let isNew = false

      if (!userData?.id) {
        const createUser: NewUser = {
          name: "",
          authId: auth.payload.sub ?? "",
          color: uniqolor.random({ lightness: [70, 90] }).color.substring(1),
          trust: 0,
        }

        await db.insert(usersSchema).values(createUser)

        userData = await db
          .select({
            id: usersSchema.id,
            name: usersSchema.name,
            email: usersSchema.email,
            color: usersSchema.color,
            avatar: usersSchema.avatar,
            trust: usersSchema.trust,
            bggUsername: usersSchema.bggUsername,
          })
          .from(usersSchema)
          .where(eq(usersSchema.authId, auth.payload.sub ?? ""))
          .then((user) => user[0])

        await db
          .insert(permissionUserToRoleSchema)
          .values({ userId: userData.id, roleId: "user", subject: "global" })

        isNew = true
      }

      const countCommunities = await db
        .select({ count: count() })
        .from(permissionUserToRoleSchema)
        .where(
          and(
            eq(permissionUserToRoleSchema.userId, userData.id),
            eq(permissionUserToRoleSchema.roleId, "owner"),
            eq(permissionUserToRoleSchema.subject, "community"),
          ),
        )
        .then((count) => count[0].count)

      return { ...userData, isNew, countCommunities }
    })

  const roles = await db
    .select({
      role: permissionUserToRoleSchema.roleId,
      subject: permissionUserToRoleSchema.subject,
      subjectId: permissionUserToRoleSchema.subjectId,
    })
    .from(permissionUserToRoleSchema)
    .where(eq(permissionUserToRoleSchema.userId, currentUser.id))
    .then(async (roles) => {
      return await Promise.all(
        roles.map(async (role) => {
          const roleSettings = await db
            .select({
              name: roleSettingsSchema.name,
              value: roleSettingsSchema.value,
            })
            .from(roleSettingsSchema)
            .where(eq(roleSettingsSchema.roleId, role.role))
            .then((settings) => settings)

          return { ...role, roleSettings }
        }),
      )
    })

  return { ...currentUser, roles }
})
