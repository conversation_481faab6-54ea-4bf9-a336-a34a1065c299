import { TRPCError, initTRPC } from "@trpc/server"
import * as trpcExpress from "@trpc/server/adapters/express"
import { eq } from "drizzle-orm"
import { AuthResult } from "express-oauth2-jwt-bearer"
import { z } from "zod"

import { db } from "../db"
import { communitySchema } from "../db/schema/community.schema"
import { permissionUserToRoleSchema } from "../db/schema/permissionUserToRole.schema"
import { roleSettingsSchema } from "../db/schema/roleSettings.schema"
import { usersSchema } from "../db/schema/users.schema"
import { hasPermission } from "../permissions"

import { fakeLogin } from "./fakeLogin"

export const createContext = ({
  req,
}: trpcExpress.CreateExpressContextOptions): {
  auth: AuthResult | undefined
} => {
  const auth = req.auth
  return {
    auth,
  }
} // no context

export type Context = Awaited<ReturnType<typeof createContext>>

const t = initTRPC.context<Context>().create()

export const router = t.router

export const publicProcedure = t.procedure

export interface Roles {
  role: string
  subject: "global" | "community" | "userdata"
  subjectId: number | null
}

const checkLogin = async (
  sub?: string,
): Promise<{ id: number; roles: Roles[] }> => {
  if (!sub) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "Unauthorized: : You are not logged in",
    })
  }

  const loginData = await db
    .select({ id: usersSchema.id })
    .from(usersSchema)
    .where(eq(usersSchema.authId, sub))

  if (!loginData) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "Unauthorized: : No such user exists!",
    })
  }

  const roles = await db
    .select({
      role: permissionUserToRoleSchema.roleId,
      subject: permissionUserToRoleSchema.subject,
      subjectId: permissionUserToRoleSchema.subjectId,
    })
    .from(permissionUserToRoleSchema)
    .where(eq(permissionUserToRoleSchema.userId, loginData[0].id))
    .then(async (roles) => {
      return await Promise.all(
        roles.map(async (role) => {
          const roleSettings = await db
            .select({
              name: roleSettingsSchema.name,
              value: roleSettingsSchema.value,
            })
            .from(roleSettingsSchema)
            .where(eq(roleSettingsSchema.roleId, role.role))
            .then((settings) => settings)

          return { ...role, roleSettings }
        }),
      )
    })

  return { ...loginData[0], roles }
}

export const protectedProcedure = t.procedure.use(
  async function isAuthed(opts) {
    if (process.env.NODE_ENV === "development") {
      return opts.next(await fakeLogin())
    }

    if (!opts.ctx.auth) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Unauthorized: : You are not logged in",
      })
    }

    const loginData = await checkLogin(opts.ctx.auth.payload?.sub)

    return opts.next({
      ctx: {
        // Infers the `session` as non-nullable
        auth: opts.ctx.auth,
        loginData,
      },
    })
  },
)

export const loginProcedure = t.procedure.use(async function isAuthed(opts) {
  if (process.env.NODE_ENV === "development") {
    return opts.next(await fakeLogin())
  }

  if (!opts.ctx.auth) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "Unauthorized: : You are not logged in: no authorization",
    })
  }

  if (!opts.ctx.auth.payload?.sub) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "Unauthorized: : You are not logged in: no sub",
    })
  }

  return opts.next({
    ctx: {
      // Infers the `session` as non-nullable
      auth: opts.ctx.auth,
    },
  })
})

// let's allow all owner/moder type commands only here
export const communityProcedure = t.procedure
  .input(z.object({ communityId: z.number() }))
  .use(async function isAuthed(opts) {
    if (process.env.NODE_ENV === "development") {
      return opts.next(await fakeLogin())
    }

    if (!opts.ctx.auth) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Unauthorized: : You are not logged in",
      })
    }
    const loginData = await checkLogin(opts.ctx?.auth?.payload?.sub)

    const { communityId } = opts.input

    if (!communityId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Unauthorized: : No Community Selected",
      })
    }

    const community = await db
      .select({
        id: communitySchema.id,
        openness: communitySchema.openness,
      })
      .from(communitySchema)
      .where(eq(communitySchema.id, communityId))
      .then((data) => data?.[0])

    if (!community) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Unauthorized: : Such community does not exist",
      })
    }

    if (
      !hasPermission(loginData, "community", "view", community) &&
      !hasPermission(loginData, "community", "viewGeneral", community)
    ) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "Forbidden: : You have no access to this community",
      })
    }

    return opts.next({
      ctx: {
        // Infers the `session` as non-nullable
        auth: opts.ctx.auth,
        loginData,
      },
    })
  })

// let's allow all owner/moder type commands only here
export const ownerProcedure = t.procedure

// let's allow all admin/superadmin type commands only here
export const adminProcedure = t.procedure
