import * as trpcExpress from "@trpc/server/adapters/express"
import bodyParser from "body-parser"
import cors from "cors"
import express from "express"
import { auth } from "express-oauth2-jwt-bearer"
import multer from "multer"

import { createContext } from "./queries/trpc"
import { appRouter } from "./router"
import { uploadHandler } from "./upload/uploadHandler"
import { uploadHandlerCommunity } from "./upload/uploadHandlerCommunity"

const app = express()

const upload = multer()

app.use(
  cors({
    origin: "*",
  }),
)

if (process.env.NODE_ENV !== "development") {
  app.use(
    auth({
      issuerBaseURL: process.env.AUTH0_ISSUER_BASE_URL,
      audience: process.env.AUTH0_AUDIENCE,
    }),
  )
}

app.use("/changeProfile", upload.single("image"), uploadHandler)
app.use("/changeCommunity", bodyParser.urlencoded({ extended: true }))
app.use("/changeCommunity", upload.single("image"), uploadHandlerCommunity)

app.use(
  "/trpc",
  trpcExpress.createExpressMiddleware({
    router: appRouter,
    createContext,
  }),
)

app.listen(8888, () => {
  console.log("API: listening on port 8888")
})
